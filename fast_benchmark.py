#!/usr/bin/env python3
"""
快速性能测试脚本 - 专门用于快速评估模型性能
减少测试时间，提供多种优化选项
"""

import os
import sys
import time
import argparse
import torch
import torch.nn as nn
import numpy as np
from mmcv import Config
from mmcv.runner import load_checkpoint

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
from models import build_posenet

def parse_args():
    parser = argparse.ArgumentParser(description='快速PCT性能测试')
    parser.add_argument('config', help='配置文件路径')
    parser.add_argument('checkpoint', help='权重文件路径')
    parser.add_argument('--device', default='cuda:0', help='测试设备')
    parser.add_argument('--input-size', type=int, default=256, help='输入图像尺寸')
    parser.add_argument('--amp', action='store_true', help='使用混合精度')
    parser.add_argument('--compile', action='store_true', help='使用torch.compile')
    parser.add_argument('--batch-sizes', type=int, nargs='+', default=[1, 8, 16, 32],
                       help='测试的批次大小')
    parser.add_argument('--iterations', type=int, default=50, help='测试迭代次数')
    parser.add_argument('--warmup', type=int, default=5, help='预热次数')
    return parser.parse_args()

def create_dummy_inputs(batch_size, input_size, device):
    """创建虚拟输入数据"""
    # 图像输入
    input_tensor = torch.randn(batch_size, 3, input_size, input_size).to(device)
    
    # 关键点数据
    num_joints = 17
    joints_3d = torch.randn(batch_size, num_joints, 3).to(device)
    joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(device)
    
    # 元数据
    img_metas = []
    for i in range(batch_size):
        img_metas.append({
            'image_file': f'dummy_{i}.jpg',
            'center': np.array([input_size//2, input_size//2]),
            'scale': np.array([1.0, 1.0]),
            'rotation': 0,
            'bbox_score': 1.0,
            'bbox_id': i,
            'flip_pairs': []
        })
    
    return input_tensor, joints_3d, joints_3d_visible, img_metas

def benchmark_batch_size(model, batch_size, input_size, device, use_amp, iterations, warmup):
    """测试特定批次大小的性能"""
    print(f"  📊 测试批次大小: {batch_size}")
    
    # 创建输入
    input_tensor, joints_3d, joints_3d_visible, img_metas = create_dummy_inputs(
        batch_size, input_size, device
    )
    
    # 预热
    model.eval()
    with torch.no_grad():
        for _ in range(warmup):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor, joints_3d=joints_3d, 
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)
                else:
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"    ❌ 内存不足，跳过批次大小 {batch_size}")
                    return None
                else:
                    raise e
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    # 性能测试
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(iterations):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor, joints_3d=joints_3d, 
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)
                else:
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"    ❌ 内存不足，跳过批次大小 {batch_size}")
                    return None
                else:
                    raise e
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    end_time = time.time()
    
    # 计算指标
    total_time = end_time - start_time
    avg_time_per_batch = total_time / iterations
    avg_time_per_image = avg_time_per_batch / batch_size
    fps = 1.0 / avg_time_per_image
    throughput = fps * batch_size
    
    result = {
        'batch_size': batch_size,
        'fps': fps,
        'latency_ms': avg_time_per_image * 1000,
        'batch_time_ms': avg_time_per_batch * 1000,
        'throughput': throughput
    }
    
    print(f"    ✅ FPS: {fps:.2f}, 延迟: {avg_time_per_image*1000:.2f}ms, 吞吐量: {throughput:.2f}")
    
    return result

def main():
    args = parse_args()
    
    print("⚡ 快速PCT性能基准测试")
    print("=" * 50)
    print(f"配置: {args.config}")
    print(f"权重: {args.checkpoint}")
    print(f"设备: {args.device}")
    print(f"输入尺寸: {args.input_size}x{args.input_size}")
    print(f"混合精度: {args.amp}")
    print(f"模型编译: {args.compile}")
    print(f"批次大小: {args.batch_sizes}")
    print(f"测试次数: {args.iterations}")
    print("=" * 50)
    
    # 设置设备
    device = torch.device(args.device)
    if device.type == 'cuda' and not torch.cuda.is_available():
        print("❌ CUDA不可用，切换到CPU")
        device = torch.device('cpu')
        args.amp = False
    
    # 加载模型
    print("🔧 加载模型...")
    cfg = Config.fromfile(args.config)
    model = build_posenet(cfg.model)
    load_checkpoint(model, args.checkpoint, map_location='cpu')
    model = model.to(device)
    model.eval()
    
    # 应用优化
    if args.compile:
        try:
            print("📦 应用torch.compile优化...")
            model = torch.compile(model, mode='max-autotune')
            print("✅ 编译优化成功")
        except Exception as e:
            print(f"❌ 编译优化失败: {e}")
    
    print("✅ 模型准备完成\n")
    
    # 测试不同批次大小
    results = []
    print("🚀 开始性能测试...")
    
    for batch_size in args.batch_sizes:
        result = benchmark_batch_size(
            model, batch_size, args.input_size, device, 
            args.amp, args.iterations, args.warmup
        )
        if result is not None:
            results.append(result)
    
    # 输出结果总结
    print("\n📊 性能测试结果总结")
    print("=" * 70)
    print(f"{'批次大小':<8} {'FPS':<8} {'延迟(ms)':<10} {'批次时间(ms)':<12} {'吞吐量':<8}")
    print("-" * 70)
    
    for result in results:
        print(f"{result['batch_size']:<8} {result['fps']:<8.2f} "
              f"{result['latency_ms']:<10.2f} {result['batch_time_ms']:<12.2f} "
              f"{result['throughput']:<8.2f}")
    
    # 找出最佳配置
    if results:
        best_fps = max(results, key=lambda x: x['fps'])
        best_throughput = max(results, key=lambda x: x['throughput'])
        
        print("\n🏆 最佳性能配置:")
        print(f"最高FPS: 批次大小={best_fps['batch_size']}, FPS={best_fps['fps']:.2f}")
        print(f"最高吞吐量: 批次大小={best_throughput['batch_size']}, 吞吐量={best_throughput['throughput']:.2f}")
    
    print("=" * 70)
    print("✅ 测试完成!")

if __name__ == '__main__':
    main()
