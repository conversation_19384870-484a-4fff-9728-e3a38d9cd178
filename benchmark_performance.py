#!/usr/bin/env python3
"""
PCT模型性能基准测试脚本
测量GFLOPs和FPS性能指标
"""

import os
import sys
import time
import argparse
import torch
import torch.nn as nn
import numpy as np
from mmcv import Config
from mmcv.runner import load_checkpoint
from mmcv.cnn import get_model_complexity_info

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
from models import build_posenet

def parse_args():
    parser = argparse.ArgumentParser(description='PCT Performance Benchmark')
    parser.add_argument('config', help='config file path')
    parser.add_argument('checkpoint', help='checkpoint file path')
    parser.add_argument('--input-shape',
                       type=int,
                       nargs='+',
                       default=[3, 256, 256],
                       help='input tensor shape (C H W)')
    parser.add_argument('--batch-size',
                       type=int,
                       default=32,
                       help='batch size for FPS testing')
    parser.add_argument('--num-warmup',
                       type=int,
                       default=10,
                       help='number of warmup iterations')
    parser.add_argument('--num-test',
                       type=int,
                       default=100,
                       help='number of test iterations for FPS')
    parser.add_argument('--device',
                       type=str,
                       default='cuda:0',
                       help='device to run benchmark')
    # 新增性能优化选项
    parser.add_argument('--fast-mode',
                       action='store_true',
                       help='enable fast testing mode (fewer iterations)')
    parser.add_argument('--skip-gflops',
                       action='store_true',
                       help='skip GFLOPs measurement to save time')
    parser.add_argument('--mixed-precision',
                       action='store_true',
                       help='use mixed precision (FP16) for faster inference')
    parser.add_argument('--compile-model',
                       action='store_true',
                       help='use torch.compile for optimization (PyTorch 2.0+)')
    # 移除TensorRT选项以避免导入错误
    parser.add_argument('--batch-sizes',
                       type=int,
                       nargs='+',
                       default=[1, 8, 16, 32],
                       help='test multiple batch sizes')
    return parser.parse_args()

def measure_gflops(model, input_shape):
    """测量模型的GFLOPs"""
    print("🔍 测量GFLOPs...")

    # 使用mmcv的复杂度分析工具
    try:
        # 创建一个简化的模型包装器，只处理图像输入
        class ModelWrapper(nn.Module):
            def __init__(self, original_model):
                super().__init__()
                self.model = original_model

            def forward(self, x):
                # 创建虚拟的其他输入
                batch_size = x.size(0)
                num_joints = 17
                joints_3d = torch.randn(batch_size, num_joints, 3).to(x.device)
                joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(x.device)
                img_metas = [{'image_file': 'dummy.jpg', 'center': [128, 128],
                             'scale': [1.0, 1.0], 'rotation': 0, 'bbox_score': 1.0,
                             'flip_pairs': []} for _ in range(batch_size)]

                return self.model(x, joints_3d=joints_3d,
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)

        wrapped_model = ModelWrapper(model)

        flops, params = get_model_complexity_info(
            wrapped_model,
            input_shape,
            print_per_layer_stat=False,
            as_strings=False
        )

        # 转换为GFLOPs
        gflops = flops / 1e9
        params_m = params / 1e6

        print(f"✅ GFLOPs: {gflops:.2f}")
        print(f"✅ Parameters: {params_m:.2f}M")

        return gflops, params_m

    except Exception as e:
        print(f"❌ GFLOPs测量失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def measure_fps(model, input_shape, batch_size, num_warmup, num_test, device, use_amp=False):
    """测量模型的FPS"""
    print(f"🔍 测量FPS (batch_size={batch_size}, AMP={use_amp})...")

    model.eval()

    # 创建随机输入
    input_tensor = torch.randn(batch_size, *input_shape).to(device)

    # 创建虚拟的关键点数据
    num_joints = 17  # COCO数据集
    joints_3d = torch.randn(batch_size, num_joints, 3).to(device)
    joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(device)

    # 创建img_metas
    img_metas = []
    for i in range(batch_size):
        img_metas.append({
            'image_file': f'dummy_{i}.jpg',
            'center': np.array([128, 128]),
            'scale': np.array([1.0, 1.0]),
            'rotation': 0,
            'bbox_score': 1.0,
            'bbox_id': i,
            'flip_pairs': []
        })

    # 预热
    print(f"🔥 预热 {num_warmup} 次...")
    with torch.no_grad():
        for _ in range(num_warmup):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor,
                                 joints_3d=joints_3d,
                                 joints_3d_visible=joints_3d_visible,
                                 img_metas=img_metas,
                                 return_loss=False)
                else:
                    _ = model(input_tensor,
                             joints_3d=joints_3d,
                             joints_3d_visible=joints_3d_visible,
                             img_metas=img_metas,
                             return_loss=False)
            except Exception as e:
                print(f"❌ 预热失败: {e}")
                return None

    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()

    # 测量FPS
    print(f"⏱️ 测量 {num_test} 次推理时间...")
    start_time = time.time()

    with torch.no_grad():
        for _ in range(num_test):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor,
                                 joints_3d=joints_3d,
                                 joints_3d_visible=joints_3d_visible,
                                 img_metas=img_metas,
                                 return_loss=False)
                else:
                    _ = model(input_tensor,
                             joints_3d=joints_3d,
                             joints_3d_visible=joints_3d_visible,
                             img_metas=img_metas,
                             return_loss=False)
            except Exception as e:
                print(f"❌ FPS测量失败: {e}")
                return None

    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()

    end_time = time.time()

    # 计算FPS
    total_time = end_time - start_time
    avg_time_per_batch = total_time / num_test
    avg_time_per_image = avg_time_per_batch / batch_size
    fps = 1.0 / avg_time_per_image

    print(f"✅ 平均每批次时间: {avg_time_per_batch*1000:.2f}ms")
    print(f"✅ 平均每张图像时间: {avg_time_per_image*1000:.2f}ms")
    print(f"✅ FPS: {fps:.2f}")

    return fps, avg_time_per_image

def optimize_model(model, args):
    """应用各种模型优化技术"""
    print("🚀 应用模型优化...")

    # 编译优化 (PyTorch 2.0+)
    if args.compile_model:
        try:
            print("📦 应用 torch.compile 优化...")
            model = torch.compile(model, mode='max-autotune')
            print("✅ torch.compile 优化成功")
        except Exception as e:
            print(f"❌ torch.compile 优化失败: {e}")

    return model

def fast_benchmark(model, input_shape, device, use_amp=False):
    """快速基准测试模式"""
    print("⚡ 快速基准测试模式")

    # 减少测试次数
    num_warmup = 3
    num_test = 20
    batch_sizes = [1, 4, 8]

    results = {}

    for batch_size in batch_sizes:
        try:
            fps, avg_time = measure_fps(
                model, input_shape, batch_size,
                num_warmup, num_test, device, use_amp
            )
            if fps is not None:
                results[batch_size] = {
                    'fps': fps,
                    'avg_time_ms': avg_time * 1000,
                    'throughput': fps * batch_size
                }
        except Exception as e:
            print(f"❌ 批次大小 {batch_size} 测试失败: {e}")
            continue

    return results

def main():
    args = parse_args()

    # 快速模式调整参数
    if args.fast_mode:
        args.num_warmup = 3
        args.num_test = 20
        print("⚡ 启用快速测试模式")

    print("🚀 PCT模型性能基准测试")
    print("=" * 50)
    print(f"配置文件: {args.config}")
    print(f"权重文件: {args.checkpoint}")
    print(f"输入形状: {args.input_shape}")
    print(f"批量大小: {args.batch_sizes}")
    print(f"设备: {args.device}")
    print(f"混合精度: {args.mixed_precision}")
    print(f"模型编译: {args.compile_model}")
    print(f"跳过GFLOPs: {args.skip_gflops}")
    print("=" * 50)

    # 检查文件是否存在
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return

    if not os.path.exists(args.checkpoint):
        print(f"❌ 权重文件不存在: {args.checkpoint}")
        return

    # 设置设备
    device = torch.device(args.device)
    if device.type == 'cuda' and not torch.cuda.is_available():
        print("❌ CUDA不可用，切换到CPU")
        device = torch.device('cpu')
        args.device = 'cpu'
        args.mixed_precision = False  # CPU不支持混合精度

    # 加载配置
    cfg = Config.fromfile(args.config)
    
    # 构建模型
    print("🔧 构建模型...")
    model = build_posenet(cfg.model)
    
    # 加载权重
    print("📦 加载权重...")
    load_checkpoint(model, args.checkpoint, map_location='cpu')
    
    # 移动到指定设备
    model = model.to(device)
    model.eval()

    # 应用优化
    model = optimize_model(model, args)

    print("✅ 模型准备完成")
    print()

    # 测量GFLOPs (可选)
    gflops, params = None, None
    if not args.skip_gflops:
        gflops, params = measure_gflops(model, tuple(args.input_shape))
        print()
    else:
        print("⏭️ 跳过GFLOPs测量")

    # 快速模式或常规模式
    if args.fast_mode:
        print("⚡ 执行快速基准测试...")
        results = fast_benchmark(model, args.input_shape, device, args.mixed_precision)

        # 输出快速测试结果
        print("\n📊 快速基准测试结果")
        print("=" * 60)
        if gflops is not None:
            print(f"GFLOPs: {gflops:.2f}")
        if params is not None:
            print(f"Parameters: {params:.2f}M")

        print("\n批次大小性能对比:")
        print(f"{'批次大小':<8} {'FPS':<8} {'延迟(ms)':<10} {'吞吐量':<10}")
        print("-" * 40)
        for batch_size, result in results.items():
            print(f"{batch_size:<8} {result['fps']:<8.2f} {result['avg_time_ms']:<10.2f} {result['throughput']:<10.2f}")

    else:
        # 常规测试模式 - 测试多个批次大小
        print("🔍 执行详细基准测试...")
        all_results = {}

        for batch_size in args.batch_sizes:
            print(f"\n--- 测试批次大小: {batch_size} ---")
            fps, avg_time = measure_fps(
                model,
                args.input_shape,
                batch_size,
                args.num_warmup,
                args.num_test,
                device,
                args.mixed_precision
            )

            if fps is not None:
                all_results[batch_size] = {
                    'fps': fps,
                    'avg_time_ms': avg_time * 1000,
                    'throughput': fps * batch_size
                }

        # 输出详细测试结果
        print("\n📊 详细基准测试结果")
        print("=" * 60)
        if gflops is not None:
            print(f"GFLOPs: {gflops:.2f}")
        if params is not None:
            print(f"Parameters: {params:.2f}M")

        if all_results:
            print(f"\n优化选项: 混合精度={args.mixed_precision}, 编译={args.compile_model}")
            print(f"{'批次大小':<8} {'FPS':<8} {'延迟(ms)':<10} {'吞吐量':<10}")
            print("-" * 40)
            for batch_size, result in all_results.items():
                print(f"{batch_size:<8} {result['fps']:<8.2f} {result['avg_time_ms']:<10.2f} {result['throughput']:<10.2f}")

    print("=" * 60)

if __name__ == '__main__':
    main()
