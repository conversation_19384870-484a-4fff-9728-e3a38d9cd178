#!/usr/bin/env python3
"""
专门测试batch size 32的性能脚本
针对大批次优化，测试吞吐量性能
"""

import os
import sys
import time
import argparse
import torch
import numpy as np
from mmcv import Config
from mmcv.runner import load_checkpoint

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
from models import build_posenet

def create_batch_inputs(batch_size, input_size, device):
    """创建批次输入数据"""
    # 图像输入
    input_tensor = torch.randn(batch_size, 3, input_size, input_size).to(device)
    
    # 关键点数据
    num_joints = 17
    joints_3d = torch.randn(batch_size, num_joints, 3).to(device)
    joints_3d_visible = torch.ones(batch_size, num_joints, 1).to(device)
    
    # 元数据
    img_metas = []
    for i in range(batch_size):
        img_metas.append({
            'image_file': f'batch_{i}.jpg',
            'center': np.array([input_size//2, input_size//2]),
            'scale': np.array([1.0, 1.0]),
            'rotation': 0,
            'bbox_score': 1.0,
            'bbox_id': i,
            'flip_pairs': []
        })
    
    return input_tensor, joints_3d, joints_3d_visible, img_metas

def test_memory_usage(model, batch_size, input_size, device):
    """测试内存使用情况"""
    if device.type != 'cuda':
        return None, None
    
    torch.cuda.empty_cache()
    torch.cuda.reset_peak_memory_stats()
    
    try:
        input_tensor, joints_3d, joints_3d_visible, img_metas = create_batch_inputs(
            batch_size, input_size, device
        )
        
        with torch.no_grad():
            _ = model(input_tensor, joints_3d=joints_3d, 
                     joints_3d_visible=joints_3d_visible,
                     img_metas=img_metas, return_loss=False)
        
        current_memory = torch.cuda.memory_allocated() / 1024**3  # GB
        peak_memory = torch.cuda.max_memory_allocated() / 1024**3  # GB
        
        return current_memory, peak_memory
        
    except RuntimeError as e:
        if "out of memory" in str(e):
            return None, None
        else:
            raise e

def benchmark_batch32(model, input_size, device, use_amp, iterations, warmup):
    """专门测试batch size 32的性能"""
    batch_size = 32
    print(f"🚀 测试批次大小 {batch_size} 的性能")
    
    # 内存测试
    print("📊 内存使用测试...")
    current_mem, peak_mem = test_memory_usage(model, batch_size, input_size, device)
    
    if current_mem is None:
        print(f"❌ 批次大小 {batch_size} 超出GPU内存限制")
        return None
    
    print(f"✅ 内存使用: 当前 {current_mem:.2f}GB, 峰值 {peak_mem:.2f}GB")
    
    # 创建输入
    input_tensor, joints_3d, joints_3d_visible, img_metas = create_batch_inputs(
        batch_size, input_size, device
    )
    
    model.eval()
    
    # 预热
    print(f"🔥 预热 {warmup} 次...")
    with torch.no_grad():
        for i in range(warmup):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor, joints_3d=joints_3d, 
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)
                else:
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
                print(f"  预热 {i+1}/{warmup} 完成")
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"❌ 预热时内存不足")
                    return None
                else:
                    raise e
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    # 性能测试
    print(f"⏱️ 性能测试 {iterations} 次...")
    start_time = time.time()
    
    with torch.no_grad():
        for i in range(iterations):
            try:
                if use_amp:
                    with torch.amp.autocast('cuda'):
                        _ = model(input_tensor, joints_3d=joints_3d, 
                                joints_3d_visible=joints_3d_visible,
                                img_metas=img_metas, return_loss=False)
                else:
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
                
                if (i + 1) % 10 == 0:
                    print(f"  完成 {i+1}/{iterations}")
                    
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"❌ 测试时内存不足")
                    return None
                else:
                    raise e
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    end_time = time.time()
    
    # 计算指标
    total_time = end_time - start_time
    avg_batch_time = total_time / iterations
    avg_image_time = avg_batch_time / batch_size
    fps = 1.0 / avg_image_time
    throughput = fps * batch_size
    
    result = {
        'batch_size': batch_size,
        'fps': fps,
        'latency_ms': avg_image_time * 1000,
        'batch_time_ms': avg_batch_time * 1000,
        'throughput': throughput,
        'memory_gb': current_mem,
        'peak_memory_gb': peak_mem
    }
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Batch Size 32 性能测试')
    parser.add_argument('--config', default='configs/pct_base_classifier.py', help='配置文件')
    parser.add_argument('--checkpoint', default='work_dirs/pct_base_classifier/best_AP_epoch_282.pth', help='权重文件')
    parser.add_argument('--device', default='cuda:0', help='设备')
    parser.add_argument('--input-size', type=int, default=256, help='输入尺寸')
    parser.add_argument('--amp', action='store_true', help='使用混合精度')
    parser.add_argument('--compile', action='store_true', help='使用torch.compile')
    parser.add_argument('--iterations', type=int, default=30, help='测试次数')
    parser.add_argument('--warmup', type=int, default=5, help='预热次数')
    args = parser.parse_args()
    
    print("🎯 Batch Size 32 专项性能测试")
    print("=" * 60)
    print(f"配置: {args.config}")
    print(f"权重: {args.checkpoint}")
    print(f"设备: {args.device}")
    print(f"输入尺寸: {args.input_size}x{args.input_size}")
    print(f"混合精度: {args.amp}")
    print(f"模型编译: {args.compile}")
    print(f"测试次数: {args.iterations}")
    print("=" * 60)
    
    # 检查文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.checkpoint):
        print(f"❌ 权重文件不存在: {args.checkpoint}")
        return
    
    # 设置设备
    device = torch.device(args.device)
    if device.type == 'cuda' and not torch.cuda.is_available():
        print("❌ CUDA不可用，batch size 32需要GPU")
        return
    
    if device.type == 'cpu':
        print("❌ CPU不适合batch size 32测试")
        return
    
    # 显示GPU信息
    if device.type == 'cuda':
        gpu_name = torch.cuda.get_device_name(device)
        gpu_memory = torch.cuda.get_device_properties(device).total_memory / 1024**3
        print(f"🔧 GPU: {gpu_name}")
        print(f"🔧 GPU内存: {gpu_memory:.1f}GB")
    
    # 加载模型
    print("\n🚀 加载模型...")
    cfg = Config.fromfile(args.config)
    model = build_posenet(cfg.model)
    load_checkpoint(model, args.checkpoint, map_location='cpu')
    model = model.to(device)
    model.eval()
    
    # 应用优化
    if args.compile:
        try:
            print("📦 应用torch.compile优化...")
            model = torch.compile(model, mode='max-autotune')
            print("✅ 编译优化成功")
        except Exception as e:
            print(f"❌ 编译优化失败: {e}")
    
    print("✅ 模型准备完成\n")
    
    # 运行测试
    result = benchmark_batch32(
        model, args.input_size, device, 
        args.amp, args.iterations, args.warmup
    )
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 Batch Size 32 测试结果")
    print("=" * 60)
    
    if result is not None:
        print(f"批次大小: {result['batch_size']}")
        print(f"FPS (单图): {result['fps']:.2f}")
        print(f"延迟 (单图): {result['latency_ms']:.2f}ms")
        print(f"批次时间: {result['batch_time_ms']:.2f}ms")
        print(f"吞吐量: {result['throughput']:.2f} 图像/秒")
        print(f"GPU内存使用: {result['memory_gb']:.2f}GB")
        print(f"GPU峰值内存: {result['peak_memory_gb']:.2f}GB")
        
        # 与batch size 1对比
        estimated_batch1_fps = result['fps']
        improvement = result['throughput'] / estimated_batch1_fps
        print(f"\n🏆 性能提升:")
        print(f"吞吐量提升: {improvement:.1f}x (相比batch size 1)")
        print(f"内存效率: {result['throughput'] / result['memory_gb']:.1f} 图像/秒/GB")
        
    else:
        print("❌ 测试失败 - 可能是内存不足")
        print("\n💡 建议:")
        print("- 尝试更小的batch size (16, 8)")
        print("- 使用混合精度 --amp")
        print("- 减小输入尺寸 --input-size 224")
    
    print("=" * 60)
    print("✅ 测试完成!")

if __name__ == '__main__':
    main()
