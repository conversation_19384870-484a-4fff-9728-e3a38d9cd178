#!/usr/bin/env python3
"""
自动化性能测试脚本
运行多种配置的性能测试并生成对比报告
"""

import os
import sys
import subprocess
import time
import json
from datetime import datetime

def run_command(cmd, description):
    """运行命令并返回结果"""
    print(f"\n🔍 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    start_time = time.time()
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"✅ 成功 (耗时: {end_time - start_time:.1f}s)")
            return result.stdout, True
        else:
            print(f"❌ 失败 (错误码: {result.returncode})")
            print(f"错误信息: {result.stderr}")
            return result.stderr, False
    except subprocess.TimeoutExpired:
        print("❌ 超时 (5分钟)")
        return "超时", False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return str(e), False

def parse_fps_from_output(output):
    """从输出中解析FPS值"""
    lines = output.split('\n')
    fps_values = []
    
    for line in lines:
        if 'FPS:' in line and '✅' in line:
            try:
                fps = float(line.split('FPS:')[1].split()[0])
                fps_values.append(fps)
            except:
                continue
    
    return fps_values

def main():
    # 检查必要文件
    config_file = "configs/pct_base_classifier.py"
    checkpoint_file = "work_dirs/pct_base_classifier/best_AP_epoch_282.pth"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    if not os.path.exists(checkpoint_file):
        print(f"❌ 权重文件不存在: {checkpoint_file}")
        return
    
    print("🚀 PCT模型自动化性能测试")
    print("=" * 60)
    print(f"配置文件: {config_file}")
    print(f"权重文件: {checkpoint_file}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        {
            "name": "基准测试",
            "cmd": f"python fast_benchmark.py {config_file} {checkpoint_file} --iterations 30 --warmup 5",
            "description": "原始模型性能"
        },
        {
            "name": "混合精度",
            "cmd": f"python fast_benchmark.py {config_file} {checkpoint_file} --amp --iterations 30 --warmup 5",
            "description": "启用FP16混合精度"
        },
        {
            "name": "模型编译",
            "cmd": f"python fast_benchmark.py {config_file} {checkpoint_file} --compile --iterations 30 --warmup 5",
            "description": "启用torch.compile优化"
        },
        {
            "name": "组合优化",
            "cmd": f"python fast_benchmark.py {config_file} {checkpoint_file} --amp --compile --iterations 30 --warmup 5",
            "description": "混合精度 + 模型编译"
        },
        {
            "name": "批次优化",
            "cmd": f"python fast_benchmark.py {config_file} {checkpoint_file} --amp --compile --batch-sizes 1 8 16 32 --iterations 20",
            "description": "测试不同批次大小"
        }
    ]
    
    # 运行测试
    results = {}
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n📊 测试 {i}/{len(test_configs)}: {config['name']}")
        output, success = run_command(config['cmd'], config['description'])
        
        if success:
            fps_values = parse_fps_from_output(output)
            results[config['name']] = {
                'success': True,
                'fps_values': fps_values,
                'max_fps': max(fps_values) if fps_values else 0,
                'avg_fps': sum(fps_values) / len(fps_values) if fps_values else 0,
                'output': output
            }
        else:
            results[config['name']] = {
                'success': False,
                'error': output,
                'fps_values': [],
                'max_fps': 0,
                'avg_fps': 0
            }
    
    # 生成报告
    print("\n" + "=" * 80)
    print("📊 性能测试结果总结")
    print("=" * 80)
    
    print(f"{'测试配置':<15} {'状态':<6} {'最高FPS':<10} {'平均FPS':<10} {'提升比例':<10}")
    print("-" * 80)
    
    baseline_fps = 0
    for name, result in results.items():
        if result['success']:
            status = "✅"
            max_fps = result['max_fps']
            avg_fps = result['avg_fps']
            
            if name == "基准测试":
                baseline_fps = avg_fps
                improvement = "基准"
            else:
                if baseline_fps > 0:
                    improvement = f"+{((avg_fps / baseline_fps - 1) * 100):.1f}%"
                else:
                    improvement = "N/A"
        else:
            status = "❌"
            max_fps = 0
            avg_fps = 0
            improvement = "失败"
        
        print(f"{name:<15} {status:<6} {max_fps:<10.2f} {avg_fps:<10.2f} {improvement:<10}")
    
    # 推荐配置
    print("\n🏆 推荐配置:")
    successful_results = {k: v for k, v in results.items() if v['success'] and v['avg_fps'] > 0}
    
    if successful_results:
        best_config = max(successful_results.items(), key=lambda x: x[1]['avg_fps'])
        print(f"最佳性能: {best_config[0]} (平均FPS: {best_config[1]['avg_fps']:.2f})")
        
        if baseline_fps > 0:
            improvement = (best_config[1]['avg_fps'] / baseline_fps - 1) * 100
            print(f"性能提升: {improvement:.1f}%")
    
    # 保存详细结果
    report_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 快速命令建议
    print("\n🛠️ 快速命令建议:")
    if successful_results:
        best_name = max(successful_results.items(), key=lambda x: x[1]['avg_fps'])[0]
        for config in test_configs:
            if config['name'] == best_name:
                print(f"最佳配置命令: {config['cmd']}")
                break
    
    print("\n✅ 所有测试完成!")

if __name__ == '__main__':
    main()
