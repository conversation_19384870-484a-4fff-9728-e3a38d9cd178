# PCT快速测试配置 - 专门用于性能测试，减少不必要的配置
_base_ = ['./coco.py']

# 基本设置
log_level = 'ERROR'  # 减少日志输出
load_from = None
resume_from = None
dist_params = dict(backend='nccl')
workflow = [('train', 1)]
find_unused_parameters = False

# 简化的数据配置
channel_cfg = dict(
    num_output_channels=17,
    dataset_joints=17,
    dataset_channel=[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]],
    inference_channel=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
)

data_cfg = dict(
    image_size=[256, 256],
    heatmap_size=[64, 64],
    num_output_channels=channel_cfg['num_output_channels'],
    num_joints=channel_cfg['dataset_joints'],
    dataset_channel=channel_cfg['dataset_channel'],
    inference_channel=channel_cfg['inference_channel'],
    soft_nms=False,
    nms_thr=1.0,
    oks_thr=0.9,
    vis_thr=0.2,
    use_gt_bbox=True,
    det_bbox_thr=0.0,
)

# 模型配置 - 与原始配置相同但移除训练相关设置
model = dict(
    type='PCT',
    pretrained=None,  # 测试时不需要预训练权重
    backbone=dict(
        type='SwinV2TransformerRPE2FC',
        embed_dim=128,
        depths=[2, 2, 18, 2],
        num_heads=[4, 8, 16, 32],
        window_size=[16, 16, 16, 8],
        pretrain_window_size=[12, 12, 12, 6],
        ape=False,
        drop_path_rate=0.3,
        patch_norm=True,
        use_checkpoint=False,  # 禁用checkpoint以提高测试速度
        rpe_interpolation='geo',
        use_shift=[True, True, False, False],
        relative_coords_table_type='norm8_log_bylayer',
        attn_type='cosine_mh',
        rpe_output_type='sigmoid',
        postnorm=True,
        mlp_type='normal',
        out_indices=(3,),
        patch_embed_type='normal',
        patch_merge_type='normal',
        strid16=False,
        frozen_stages=-1,  # 不冻结任何层
    ),
    keypoint_head=dict(
        type='PCT_Head',
        in_channels=1024,
        out_channels=channel_cfg['num_output_channels'],
        num_deconv_layers=3,
        num_deconv_filters=(256, 256, 256),
        num_deconv_kernels=(4, 4, 4),
        extra=dict(final_conv_kernel=1, ),
        loss_keypoint=dict(type='JointsMSELoss', use_target_weight=True),
        guide_ratio=0.5,
        tokenizer=dict(
            type='PCT_Tokenizer',
            in_channels=1024,
            heatmap_size=data_cfg['heatmap_size'],
            heatmap_channels=channel_cfg['num_output_channels'],
            apply_ohkm=True,
            loss_factor=1.0,
            codebook=dict(
                token_num=34,
                token_dim=512,
                token_class_num=2048,
                ema_decay=0.9,
            ),
            loss_keypoint=dict(
                type='Tokenizer_loss',
                joint_loss_w=1.0, 
                e_loss_w=15.0,
                beta=0.05,
            )
        )
    ),
    train_cfg=dict(),
    test_cfg=dict(
        flip_test=False,  # 禁用翻转测试以提高速度
        post_process='default',
        shift_heatmap=True,
        modulate_kernel=11,
        dataset_name='COCO'
    )
)

# 移除所有训练相关配置
# 这个配置文件专门用于推理和性能测试
