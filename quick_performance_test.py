#!/usr/bin/env python3
"""
一键快速性能测试脚本
最快速度测试您的PCT模型性能，提供立即可用的优化建议
"""

import os
import sys
import time
import argparse
import torch
import numpy as np
from mmcv import Config
from mmcv.runner import load_checkpoint

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))
from models import build_posenet

def quick_test(model, device, use_amp=False, batch_size=1, iterations=10):
    """超快速性能测试"""
    input_size = 256
    
    # 创建输入
    input_tensor = torch.randn(batch_size, 3, input_size, input_size).to(device)
    joints_3d = torch.randn(batch_size, 17, 3).to(device)
    joints_3d_visible = torch.ones(batch_size, 17, 1).to(device)
    img_metas = [{
        'image_file': f'test_{i}.jpg',
        'center': np.array([128, 128]),
        'scale': np.array([1.0, 1.0]),
        'rotation': 0,
        'bbox_score': 1.0,
        'bbox_id': i,
        'flip_pairs': []
    } for i in range(batch_size)]
    
    model.eval()
    
    # 预热
    with torch.no_grad():
        for _ in range(3):
            if use_amp:
                with torch.amp.autocast('cuda'):
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
            else:
                _ = model(input_tensor, joints_3d=joints_3d, 
                        joints_3d_visible=joints_3d_visible,
                        img_metas=img_metas, return_loss=False)
    
    # 同步
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    # 测试
    start_time = time.time()
    with torch.no_grad():
        for _ in range(iterations):
            if use_amp:
                with torch.amp.autocast('cuda'):
                    _ = model(input_tensor, joints_3d=joints_3d, 
                            joints_3d_visible=joints_3d_visible,
                            img_metas=img_metas, return_loss=False)
            else:
                _ = model(input_tensor, joints_3d=joints_3d, 
                        joints_3d_visible=joints_3d_visible,
                        img_metas=img_metas, return_loss=False)
    
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    end_time = time.time()
    
    # 计算FPS
    total_time = end_time - start_time
    avg_time = total_time / iterations / batch_size
    fps = 1.0 / avg_time
    
    return fps, avg_time * 1000

def main():
    parser = argparse.ArgumentParser(description='一键快速性能测试')
    parser.add_argument('--config', default='configs/pct_base_classifier.py', help='配置文件')
    parser.add_argument('--checkpoint', default='work_dirs/pct_base_classifier/best_AP_epoch_282.pth', help='权重文件')
    parser.add_argument('--device', default='cuda:0', help='设备')
    args = parser.parse_args()
    
    print("⚡ 一键快速性能测试")
    print("=" * 50)
    
    # 检查文件
    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.checkpoint):
        print(f"❌ 权重文件不存在: {args.checkpoint}")
        return
    
    # 设置设备
    device = torch.device(args.device)
    if device.type == 'cuda' and not torch.cuda.is_available():
        print("❌ CUDA不可用，使用CPU")
        device = torch.device('cpu')
    
    print(f"🔧 设备: {device}")
    print(f"📁 配置: {args.config}")
    print(f"📦 权重: {args.checkpoint}")
    
    # 加载模型
    print("\n🚀 加载模型...")
    cfg = Config.fromfile(args.config)
    model = build_posenet(cfg.model)
    load_checkpoint(model, args.checkpoint, map_location='cpu')
    model = model.to(device)
    
    print("✅ 模型加载完成")
    
    # 测试配置
    tests = [
        ("基准测试", False, False),
        ("混合精度", True, False),
        ("模型编译", False, True),
        ("组合优化", True, True),
    ]
    
    results = {}
    
    for test_name, use_amp, use_compile in tests:
        print(f"\n📊 {test_name}...")
        
        # 应用优化
        test_model = model
        if use_compile:
            try:
                test_model = torch.compile(model, mode='max-autotune')
            except:
                print("  ⚠️ 编译失败，跳过")
                continue
        
        if device.type == 'cpu':
            use_amp = False  # CPU不支持AMP
        
        try:
            fps, latency = quick_test(test_model, device, use_amp, batch_size=1, iterations=10)
            results[test_name] = {'fps': fps, 'latency': latency}
            print(f"  ✅ FPS: {fps:.2f}, 延迟: {latency:.2f}ms")
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            results[test_name] = {'fps': 0, 'latency': 0}
    
    # 结果总结
    print("\n" + "=" * 60)
    print("📊 快速测试结果总结")
    print("=" * 60)
    
    if results:
        baseline_fps = results.get("基准测试", {}).get('fps', 0)
        
        print(f"{'配置':<12} {'FPS':<8} {'延迟(ms)':<10} {'提升':<10}")
        print("-" * 50)
        
        for name, result in results.items():
            fps = result['fps']
            latency = result['latency']
            
            if fps > 0:
                if name == "基准测试":
                    improvement = "基准"
                elif baseline_fps > 0:
                    improvement = f"+{((fps/baseline_fps-1)*100):.1f}%"
                else:
                    improvement = "N/A"
                
                print(f"{name:<12} {fps:<8.2f} {latency:<10.2f} {improvement:<10}")
            else:
                print(f"{name:<12} {'失败':<8} {'失败':<10} {'失败':<10}")
    
    # 推荐
    print("\n🏆 推荐配置:")
    successful_tests = {k: v for k, v in results.items() if v['fps'] > 0}
    
    if successful_tests:
        best_test = max(successful_tests.items(), key=lambda x: x[1]['fps'])
        print(f"最佳性能: {best_test[0]} (FPS: {best_test[1]['fps']:.2f})")
        
        if baseline_fps > 0:
            improvement = (best_test[1]['fps'] / baseline_fps - 1) * 100
            print(f"性能提升: {improvement:.1f}%")
        
        # 给出具体命令
        print(f"\n🛠️ 使用最佳配置运行详细测试:")
        if best_test[0] == "混合精度":
            print(f"python fast_benchmark.py {args.config} {args.checkpoint} --amp")
        elif best_test[0] == "模型编译":
            print(f"python fast_benchmark.py {args.config} {args.checkpoint} --compile")
        elif best_test[0] == "组合优化":
            print(f"python fast_benchmark.py {args.config} {args.checkpoint} --amp --compile")
        else:
            print(f"python fast_benchmark.py {args.config} {args.checkpoint}")
    
    print("\n✅ 快速测试完成!")
    print("\n💡 提示:")
    print("- 运行 'python fast_benchmark.py --help' 查看更多选项")
    print("- 运行 'python run_performance_tests.py' 进行全面测试")
    print("- 查看 'performance_optimization_guide.md' 了解更多优化方法")

if __name__ == '__main__':
    main()
