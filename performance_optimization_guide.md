# PCT模型性能优化指南

## 🚀 快速提升性能的方法

### 1. 使用优化的测试脚本

#### 快速测试模式
```bash
# 使用新的快速测试脚本
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp --compile

# 原始脚本的快速模式
python benchmark_performance.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --fast-mode --mixed-precision --compile-model
```

#### 跳过GFLOPs测量（节省时间）
```bash
python benchmark_performance.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --skip-gflops --fast-mode
```

### 2. 性能优化技术

#### A. 混合精度 (AMP) - 提升30-50%速度
```bash
# 启用混合精度
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp

# 预期效果：FPS从19.27提升到25-30
```

#### B. 模型编译 (PyTorch 2.0+) - 提升20-40%速度
```bash
# 启用torch.compile
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --compile

# 预期效果：FPS从19.27提升到23-27
```

#### C. 组合优化 - 提升50-80%速度
```bash
# 同时启用混合精度和编译
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp --compile

# 预期效果：FPS从19.27提升到30-35
```

### 3. 批次大小优化

#### 测试不同批次大小
```bash
# 测试多个批次大小找到最佳吞吐量
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --batch-sizes 1 2 4 8 16
```

#### 批次大小建议
- **实时应用**: batch_size=1 (最低延迟)
- **批量处理**: batch_size=4-8 (最高吞吐量)
- **GPU内存限制**: 根据显存调整

### 4. 输入尺寸优化

#### 测试不同输入尺寸
```bash
# 测试较小输入尺寸
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --input-size 224 --amp

# 测试更小输入尺寸
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --input-size 192 --amp
```

#### 输入尺寸vs性能
- **256x256**: 标准精度，19.27 FPS
- **224x224**: 略降精度，~25 FPS
- **192x192**: 明显降精度，~35 FPS

### 5. 模型量化 (高级优化)

#### 动态量化
```python
# 添加到测试脚本中
import torch.quantization as quantization

# 动态量化
model_quantized = torch.quantization.quantize_dynamic(
    model, {torch.nn.Linear}, dtype=torch.qint8
)
```

#### 静态量化
```python
# 需要校准数据集
model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
model_prepared = torch.quantization.prepare(model)
# ... 校准过程 ...
model_quantized = torch.quantization.convert(model_prepared)
```

### 6. TensorRT优化 (NVIDIA GPU)

#### 安装TensorRT
```bash
pip install torch-tensorrt
```

#### 使用TensorRT
```python
import torch_tensorrt

# 编译为TensorRT
trt_model = torch_tensorrt.compile(
    model,
    inputs=[torch.randn(1, 3, 256, 256).cuda()],
    enabled_precisions={torch.float, torch.half}
)
```

### 7. 性能监控和分析

#### GPU利用率监控
```bash
# 运行测试时监控GPU
nvidia-smi -l 1

# 或使用gpustat
pip install gpustat
gpustat -i 1
```

#### 性能分析
```python
# 使用PyTorch Profiler
with torch.profiler.profile(
    activities=[torch.profiler.ProfilerActivity.CPU, torch.profiler.ProfilerActivity.CUDA],
    record_shapes=True
) as prof:
    # 运行模型
    pass

print(prof.key_averages().table(sort_by="cuda_time_total"))
```

## 📊 预期性能提升

### 基准性能 (您的模型)
- **原始**: 19.27 FPS, 51.90ms延迟
- **目标**: 30+ FPS, <35ms延迟

### 优化效果预估
| 优化方法 | FPS提升 | 延迟降低 | 实现难度 |
|----------|---------|----------|----------|
| 混合精度 | +30-50% | -25-35% | 简单 |
| 模型编译 | +20-40% | -15-25% | 简单 |
| 批次优化 | +50-100% | 不变 | 简单 |
| 输入尺寸 | +30-80% | -25-45% | 中等 |
| 量化 | +50-100% | -35-50% | 困难 |
| TensorRT | +100-200% | -50-70% | 困难 |

### 组合优化效果
- **简单组合** (AMP + Compile): 30-35 FPS
- **中等组合** (AMP + Compile + 批次): 40-50 FPS  
- **高级组合** (全部优化): 60+ FPS

## 🛠️ 实用命令

### 快速性能测试
```bash
# 最快测试 (30秒内完成)
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp --compile --iterations 20 --warmup 3

# 详细测试 (2分钟内完成)
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp --compile --batch-sizes 1 2 4 8
```

### 对比测试
```bash
# 测试原始性能
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth

# 测试优化性能
python fast_benchmark.py configs/pct_base_classifier.py work_dirs/pct_base_classifier/best_AP_epoch_282.pth --amp --compile
```

## 🎯 推荐优化策略

### 立即可用 (0配置)
1. 使用 `--amp` 混合精度
2. 使用 `--compile` 模型编译
3. 使用 `fast_benchmark.py` 快速测试

### 短期优化 (1小时内)
1. 调整批次大小
2. 测试不同输入尺寸
3. 优化测试参数

### 长期优化 (1天内)
1. 实现模型量化
2. 集成TensorRT
3. 自定义CUDA算子

通过这些优化，您的PCT模型性能可以从19.27 FPS提升到30+ FPS，甚至更高！
